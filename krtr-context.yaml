# KRTR Mesh context configuration for context.app
name: KRTR Mesh
path: ~/krtr-mesh-local
apps: [VSCode, Terminal, Xcode, Arc]
urls:
  - https://github.com/Z0rlord/krtr-mesh
  - https://developer.apple.com/documentation/multipeerconnectivity
  - https://developer.apple.com/documentation/corebluetooth
  - https://noir-lang.org/
scripts:
  # Swift development scripts
  - cd ~/krtr-mesh-local && swift build
  - cd ~/krtr-mesh-local && swift test
  - cd ~/krtr-mesh-local && xcodebuild -project KRTRMesh.xcodeproj -scheme KRTRMesh build
  
  # iOS native development
  - cd ~/krtr-mesh-local/krtr-native-ios && xcodebuild -project KRTR.xcodeproj -scheme KRTR build
  
  # BitChat base development
  - cd ~/krtr-mesh-local/bitchat-base && swift build
  - cd ~/krtr-mesh-local/bitchat-base && xcodebuild -project bitchat.xcodeproj build
  
  # Circuit compilation (ZK proofs)
  - cd ~/krtr-mesh-local/circuits && find . -name "*.nr" -exec nargo compile {} \;
  
  # Landing page development
  - cd ~/krtr-mesh-local/landing-page && npm install && npm run dev
  
  # Development shell setup
  - cd ~/krtr-mesh-local && chmod +x scripts/dev-shell.sh && ./scripts/dev-shell.sh
