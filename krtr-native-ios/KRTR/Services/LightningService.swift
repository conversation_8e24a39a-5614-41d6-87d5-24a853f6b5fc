//
// LightningService.swift
// KRTR
//
// Lightning Network service implementation
// Supports both LND and Core Lightning backends
//

import Foundation
import SwiftUI
import Combine

// MARK: - Lightning Service Protocol

/// Protocol defining Lightning Network operations
protocol LightningServiceProtocol: ObservableObject {
    var isConnected: Bool { get }
    var nodeInfo: NodeInfo? { get }
    var recentZaps: [ZapTransaction] { get }
    var balance: Int64 { get }
    
    /// Connect to Lightning node
    func connect() async throws
    
    /// Disconnect from Lightning node
    func disconnect()
    
    /// Get node information
    func getNodeInfo() async throws -> NodeInfo
    
    /// Generate Lightning invoice
    func createInvoice(amount: Int64, memo: String?) async throws -> LightningInvoice
    
    /// Pay Lightning invoice
    func payInvoice(_ paymentRequest: String) async throws -> PaymentResult
    
    /// Check payment status
    func checkPaymentStatus(paymentHash: String) async throws -> PaymentStatus
    
    /// Generate LNURL for receiving payments
    func generateLNURL(amount: Int64?, memo: String?) async throws -> String
    
    /// Send zap payment
    func sendZap(to address: String, amount: Int64, memo: String?) async throws -> ZapTransaction
    
    /// Get wallet balance
    func getBalance() async throws -> Int64
    
    /// Get channel information
    func getChannels() async throws -> [ChannelInfo]
}

// MARK: - Lightning Service Implementation

@MainActor
class LightningService: LightningServiceProtocol {
    
    // MARK: - Published Properties
    
    @Published var isConnected: Bool = false
    @Published var nodeInfo: NodeInfo?
    @Published var recentZaps: [ZapTransaction] = []
    @Published var balance: Int64 = 0
    @Published var channels: [ChannelInfo] = []
    
    // MARK: - Private Properties
    
    private let config: LightningConfig
    private var urlSession: URLSession
    private var cancellables = Set<AnyCancellable>()
    
    // Integration with existing KRTR services
    private weak var meshService: BluetoothMeshService?
    
    // MARK: - Initialization
    
    init(config: LightningConfig, meshService: BluetoothMeshService? = nil) {
        self.config = config
        self.meshService = meshService
        
        // Configure URL session with custom settings
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = config.timeout
        configuration.timeoutIntervalForResource = config.timeout * 2
        
        // Add certificate pinning for LND if needed
        if config.nodeType == .lnd, let certPath = config.certificatePath {
            // TODO: Implement certificate pinning
        }
        
        self.urlSession = URLSession(configuration: configuration)
        
        // Start periodic status updates
        startStatusUpdates()
    }
    
    deinit {
        disconnect()
    }
    
    // MARK: - Connection Management
    
    func connect() async throws {
        do {
            let info = try await getNodeInfo()
            await MainActor.run {
                self.nodeInfo = info
                self.isConnected = true
            }
            
            // Update balance and channels
            try await updateBalance()
            try await updateChannels()
            
            print("✅ Connected to Lightning node: \(info.alias)")
            
        } catch {
            await MainActor.run {
                self.isConnected = false
                self.nodeInfo = nil
            }
            throw LightningError.nodeNotConnected
        }
    }
    
    func disconnect() {
        isConnected = false
        nodeInfo = nil
        cancellables.removeAll()
    }
    
    // MARK: - Node Information
    
    func getNodeInfo() async throws -> NodeInfo {
        let endpoint = config.nodeType == .lnd ? "/v1/getinfo" : "/v1/getinfo"
        let url = config.baseURL.appendingPathComponent(endpoint)
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // Add authentication headers
        try addAuthHeaders(to: &request)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw LightningError.networkError(URLError(.badServerResponse))
        }
        
        let nodeInfo = try JSONDecoder().decode(NodeInfo.self, from: data)
        return nodeInfo
    }
    
    // MARK: - Invoice Operations
    
    func createInvoice(amount: Int64, memo: String?) async throws -> LightningInvoice {
        let endpoint = config.nodeType == .lnd ? "/v1/invoices" : "/v1/invoice/genInvoice"
        let url = config.baseURL.appendingPathComponent(endpoint)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        try addAuthHeaders(to: &request)
        
        let requestBody: [String: Any] = [
            "value": amount,
            "memo": memo ?? "",
            "expiry": 3600 // 1 hour expiry
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw LightningError.networkError(URLError(.badServerResponse))
        }
        
        let invoice = try JSONDecoder().decode(LightningInvoice.self, from: data)
        return invoice
    }
    
    func payInvoice(_ paymentRequest: String) async throws -> PaymentResult {
        let endpoint = config.nodeType == .lnd ? "/v1/channels/transactions" : "/v1/pay"
        let url = config.baseURL.appendingPathComponent(endpoint)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        try addAuthHeaders(to: &request)
        
        let requestBody: [String: Any] = [
            "payment_request": paymentRequest
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw LightningError.paymentFailed("HTTP \(response)")
        }
        
        let result = try JSONDecoder().decode(PaymentResult.self, from: data)
        return result
    }
    
    func checkPaymentStatus(paymentHash: String) async throws -> PaymentStatus {
        let endpoint = config.nodeType == .lnd ? "/v1/invoice/\(paymentHash)" : "/v1/invoice/\(paymentHash)"
        let url = config.baseURL.appendingPathComponent(endpoint)
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        try addAuthHeaders(to: &request)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw LightningError.networkError(URLError(.badServerResponse))
        }
        
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        let settled = json?["settled"] as? Bool ?? false
        
        return settled ? .succeeded : .pending
    }
    
    // MARK: - LNURL Operations
    
    func generateLNURL(amount: Int64?, memo: String?) async throws -> String {
        // TODO: Implement LNURL generation
        // This would typically involve setting up LNURL endpoints
        throw LightningError.unknownError("LNURL not implemented yet")
    }
    
    // MARK: - Zap Operations
    
    func sendZap(to address: String, amount: Int64, memo: String?) async throws -> ZapTransaction {
        // Create zap transaction record
        let zapTransaction = ZapTransaction(
            amount: amount,
            recipient: address,
            memo: memo,
            status: .pending
        )
        
        // Add to recent zaps
        await MainActor.run {
            self.recentZaps.insert(zapTransaction, at: 0)
        }
        
        do {
            // For now, treat address as Lightning invoice
            // TODO: Implement proper Lightning address resolution
            let result = try await payInvoice(address)
            
            // Update transaction status
            let updatedZap = ZapTransaction(
                id: zapTransaction.id,
                amount: amount,
                recipient: address,
                memo: memo,
                status: result.success ? .succeeded : .failed,
                paymentHash: result.paymentHash,
                preimage: result.preimage
            )
            
            await MainActor.run {
                if let index = self.recentZaps.firstIndex(where: { $0.id == zapTransaction.id }) {
                    self.recentZaps[index] = updatedZap
                }
            }
            
            // Broadcast zap notification through mesh if available
            broadcastZapNotification(updatedZap)
            
            return updatedZap
            
        } catch {
            // Update transaction as failed
            let failedZap = ZapTransaction(
                id: zapTransaction.id,
                amount: amount,
                recipient: address,
                memo: memo,
                status: .failed
            )
            
            await MainActor.run {
                if let index = self.recentZaps.firstIndex(where: { $0.id == zapTransaction.id }) {
                    self.recentZaps[index] = failedZap
                }
            }
            
            throw error
        }
    }
    
    // MARK: - Balance and Channels
    
    func getBalance() async throws -> Int64 {
        let endpoint = config.nodeType == .lnd ? "/v1/balance/channels" : "/v1/getBalance"
        let url = config.baseURL.appendingPathComponent(endpoint)
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        try addAuthHeaders(to: &request)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw LightningError.networkError(URLError(.badServerResponse))
        }
        
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        let balance = json?["balance"] as? Int64 ?? 0
        
        await MainActor.run {
            self.balance = balance
        }
        
        return balance
    }
    
    func getChannels() async throws -> [ChannelInfo] {
        let endpoint = config.nodeType == .lnd ? "/v1/channels" : "/v1/channel/listChannels"
        let url = config.baseURL.appendingPathComponent(endpoint)
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        try addAuthHeaders(to: &request)
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw LightningError.networkError(URLError(.badServerResponse))
        }
        
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        let channelsData = json?["channels"] as? [[String: Any]] ?? []
        
        let channels = try channelsData.compactMap { channelData -> ChannelInfo? in
            let jsonData = try JSONSerialization.data(withJSONObject: channelData)
            return try? JSONDecoder().decode(ChannelInfo.self, from: jsonData)
        }
        
        await MainActor.run {
            self.channels = channels
        }
        
        return channels
    }

    // MARK: - Private Helper Methods

    private func addAuthHeaders(to request: inout URLRequest) throws {
        switch config.nodeType {
        case .lnd:
            // LND uses macaroon authentication
            if let macaroonPath = config.macaroonPath {
                // TODO: Load macaroon from file and add to headers
                // request.setValue("Bearer \(macaroonHex)", forHTTPHeaderField: "Authorization")
            }

        case .coreLightning:
            // Core Lightning uses API key or rune authentication
            if let apiKey = config.apiKey {
                request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
            }
        }
    }

    private func startStatusUpdates() {
        // Update status every 30 seconds
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    await self?.updateStatus()
                }
            }
            .store(in: &cancellables)
    }

    private func updateStatus() async {
        guard isConnected else { return }

        do {
            try await updateBalance()
            try await updateChannels()
        } catch {
            print("⚠️ Failed to update Lightning status: \(error)")
        }
    }

    private func updateBalance() async throws {
        _ = try await getBalance()
    }

    private func updateChannels() async throws {
        _ = try await getChannels()
    }

    private func broadcastZapNotification(_ zap: ZapTransaction) {
        // Integration with existing mesh service
        guard let meshService = meshService else { return }

        // Create a mesh message for the zap notification
        let zapData: [String: Any] = [
            "type": "zap_notification",
            "amount": zap.amount,
            "recipient": zap.recipient,
            "memo": zap.memo ?? "",
            "status": zap.status.rawValue,
            "timestamp": zap.timestamp.timeIntervalSince1970
        ]

        if let jsonData = try? JSONSerialization.data(withJSONObject: zapData),
           let jsonString = String(data: jsonData, encoding: .utf8) {

            // Send through mesh network with Lightning channel
            meshService.sendMessage(
                jsonString,
                mentions: [],
                channel: "#lightning",
                messageID: zap.id
            )
        }
    }
}

// MARK: - Lightning Service Factory

class LightningServiceFactory {

    static func createService(
        nodeType: LightningNodeType = .lnd,
        baseURL: URL,
        apiKey: String? = nil,
        meshService: BluetoothMeshService? = nil
    ) -> LightningService {

        let config = LightningConfig(
            nodeType: nodeType,
            baseURL: baseURL,
            apiKey: apiKey
        )

        return LightningService(config: config, meshService: meshService)
    }

    static func createTestService() -> LightningService {
        // Create a test service for development
        guard let testURL = URL(string: "https://localhost:8080") else {
            fatalError("Invalid test URL")
        }

        let config = LightningConfig(
            nodeType: .lnd,
            baseURL: testURL,
            apiKey: "test_key"
        )

        return LightningService(config: config)
    }
}

// MARK: - Lightning Service Extensions

extension LightningService {

    /// Create a zap with ZK proof integration
    func createZKProofZap(
        to address: String,
        amount: Int64,
        memo: String?,
        zkProofHash: String
    ) async throws -> ZapTransaction {

        let zapTransaction = ZapTransaction(
            amount: amount,
            recipient: address,
            memo: memo,
            status: .pending,
            zkProofHash: zkProofHash
        )

        // Add to recent zaps
        await MainActor.run {
            self.recentZaps.insert(zapTransaction, at: 0)
        }

        // TODO: Implement ZK proof verification before payment
        // This would integrate with the existing ZKService

        do {
            let result = try await payInvoice(address)

            let updatedZap = ZapTransaction(
                id: zapTransaction.id,
                amount: amount,
                recipient: address,
                memo: memo,
                status: result.success ? .succeeded : .failed,
                paymentHash: result.paymentHash,
                preimage: result.preimage,
                zkProofHash: zkProofHash
            )

            await MainActor.run {
                if let index = self.recentZaps.firstIndex(where: { $0.id == zapTransaction.id }) {
                    self.recentZaps[index] = updatedZap
                }
            }

            broadcastZapNotification(updatedZap)
            return updatedZap

        } catch {
            let failedZap = ZapTransaction(
                id: zapTransaction.id,
                amount: amount,
                recipient: address,
                memo: memo,
                status: .failed,
                zkProofHash: zkProofHash
            )

            await MainActor.run {
                if let index = self.recentZaps.firstIndex(where: { $0.id == zapTransaction.id }) {
                    self.recentZaps[index] = failedZap
                }
            }

            throw error
        }
    }

    /// Get zap statistics
    func getZapStats() -> (sent: Int, received: Int, totalAmount: Int64) {
        let sentZaps = recentZaps.filter { $0.sender != nil }
        let receivedZaps = recentZaps.filter { $0.sender == nil }
        let totalAmount = recentZaps.reduce(0) { $0 + $1.amount }

        return (sent: sentZaps.count, received: receivedZaps.count, totalAmount: totalAmount)
    }
}
