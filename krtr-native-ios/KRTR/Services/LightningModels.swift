//
// LightningModels.swift
// KRTR
//
// Lightning Network data models and types
// Supports both LND and Core Lightning backends
//

import Foundation
import SwiftUI

// MARK: - Core Lightning Types

/// Lightning payment status
enum PaymentStatus: String, CaseIterable, Codable {
    case pending = "pending"
    case succeeded = "succeeded"
    case failed = "failed"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .pending: return "Pending"
        case .succeeded: return "Succeeded"
        case .failed: return "Failed"
        case .cancelled: return "Cancelled"
        }
    }
    
    var color: Color {
        switch self {
        case .pending: return .orange
        case .succeeded: return .green
        case .failed: return .red
        case .cancelled: return .gray
        }
    }
}

/// Lightning node information
struct NodeInfo: Codable, Identifiable {
    let id: String
    let alias: String
    let publicKey: String
    let version: String
    let blockHeight: Int
    let channelCount: Int
    let activeChannelCount: Int
    let totalCapacity: Int64
    let isOnline: Bool
    
    enum CodingKeys: String, CodingKey {
        case id = "identity_pubkey"
        case alias
        case publicKey = "identity_pubkey"
        case version
        case blockHeight = "block_height"
        case channelCount = "num_channels"
        case activeChannelCount = "num_active_channels"
        case totalCapacity = "total_capacity_sat"
        case isOnline = "synced_to_chain"
    }
}

/// Lightning invoice
struct LightningInvoice: Codable, Identifiable {
    let id: String
    let paymentRequest: String
    let paymentHash: String
    let amount: Int64
    let memo: String?
    let expiry: Date
    let isSettled: Bool
    let settledDate: Date?
    
    enum CodingKeys: String, CodingKey {
        case id = "r_hash"
        case paymentRequest = "payment_request"
        case paymentHash = "r_hash"
        case amount = "value"
        case memo
        case expiry
        case isSettled = "settled"
        case settledDate = "settle_date"
    }
}

/// Zap transaction record
struct ZapTransaction: Codable, Identifiable {
    let id: String
    let amount: Int64
    let sender: String?
    let recipient: String
    let memo: String?
    let timestamp: Date
    let status: PaymentStatus
    let paymentHash: String?
    let preimage: String?
    
    // Optional ZK proof integration
    let zkProofHash: String?
    
    init(
        id: String = UUID().uuidString,
        amount: Int64,
        sender: String? = nil,
        recipient: String,
        memo: String? = nil,
        timestamp: Date = Date(),
        status: PaymentStatus = .pending,
        paymentHash: String? = nil,
        preimage: String? = nil,
        zkProofHash: String? = nil
    ) {
        self.id = id
        self.amount = amount
        self.sender = sender
        self.recipient = recipient
        self.memo = memo
        self.timestamp = timestamp
        self.status = status
        self.paymentHash = paymentHash
        self.preimage = preimage
        self.zkProofHash = zkProofHash
    }
}

/// Lightning address (LNURL-pay)
struct LightningAddress: Codable {
    let address: String // e.g., "<EMAIL>"
    let lnurl: String
    let minSendable: Int64
    let maxSendable: Int64
    let metadata: String
    let callback: URL
    
    var isValid: Bool {
        address.contains("@") && !lnurl.isEmpty
    }
}

/// Payment result
struct PaymentResult: Codable {
    let success: Bool
    let paymentHash: String?
    let preimage: String?
    let error: String?
    let feePaid: Int64?
    
    init(success: Bool, paymentHash: String? = nil, preimage: String? = nil, error: String? = nil, feePaid: Int64? = nil) {
        self.success = success
        self.paymentHash = paymentHash
        self.preimage = preimage
        self.error = error
        self.feePaid = feePaid
    }
}

/// Lightning channel information
struct ChannelInfo: Codable, Identifiable {
    let id: String
    let remotePubkey: String
    let channelPoint: String
    let capacity: Int64
    let localBalance: Int64
    let remoteBalance: Int64
    let isActive: Bool
    let isPrivate: Bool
    
    enum CodingKeys: String, CodingKey {
        case id = "chan_id"
        case remotePubkey = "remote_pubkey"
        case channelPoint = "channel_point"
        case capacity
        case localBalance = "local_balance"
        case remoteBalance = "remote_balance"
        case isActive = "active"
        case isPrivate = "private"
    }
}

// MARK: - Lightning Service Configuration

/// Lightning service configuration
struct LightningConfig {
    let nodeType: LightningNodeType
    let baseURL: URL
    let apiKey: String?
    let certificatePath: String?
    let macaroonPath: String?
    let timeout: TimeInterval
    
    init(
        nodeType: LightningNodeType = .lnd,
        baseURL: URL,
        apiKey: String? = nil,
        certificatePath: String? = nil,
        macaroonPath: String? = nil,
        timeout: TimeInterval = 30.0
    ) {
        self.nodeType = nodeType
        self.baseURL = baseURL
        self.apiKey = apiKey
        self.certificatePath = certificatePath
        self.macaroonPath = macaroonPath
        self.timeout = timeout
    }
}

/// Supported Lightning node types
enum LightningNodeType: String, CaseIterable {
    case lnd = "lnd"
    case coreLightning = "core_lightning"
    
    var displayName: String {
        switch self {
        case .lnd: return "LND"
        case .coreLightning: return "Core Lightning"
        }
    }
}

// MARK: - Error Types

/// Lightning service errors
enum LightningError: LocalizedError {
    case nodeNotConnected
    case invalidInvoice
    case insufficientBalance
    case paymentFailed(String)
    case networkError(Error)
    case invalidConfiguration
    case authenticationFailed
    case invoiceExpired
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        case .nodeNotConnected:
            return "Lightning node is not connected"
        case .invalidInvoice:
            return "Invalid Lightning invoice"
        case .insufficientBalance:
            return "Insufficient balance for payment"
        case .paymentFailed(let reason):
            return "Payment failed: \(reason)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .invalidConfiguration:
            return "Invalid Lightning configuration"
        case .authenticationFailed:
            return "Authentication failed"
        case .invoiceExpired:
            return "Invoice has expired"
        case .unknownError(let message):
            return "Unknown error: \(message)"
        }
    }
}
