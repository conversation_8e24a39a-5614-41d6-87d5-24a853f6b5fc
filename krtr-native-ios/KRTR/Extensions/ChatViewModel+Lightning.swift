//
// ChatViewModel+Lightning.swift
// KRTR
//
// Lightning Network integration extensions for ChatViewModel
//

import Foundation
import SwiftUI

// MARK: - Lightning Integration Extension

extension ChatViewModel {
    
    /// Handle incoming Lightning zap notifications from mesh network
    func handleLightningMessage(_ messageContent: String) {
        guard let data = messageContent.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let type = json["type"] as? String,
              type == "zap_notification" else {
            return
        }
        
        // Parse zap notification
        guard let amount = json["amount"] as? Int64,
              let recipient = json["recipient"] as? String,
              let statusString = json["status"] as? String,
              let status = PaymentStatus(rawValue: statusString),
              let timestamp = json["timestamp"] as? TimeInterval else {
            return
        }
        
        let memo = json["memo"] as? String
        let zapDate = Date(timeIntervalSince1970: timestamp)
        
        // Create a chat message for the zap notification
        let zapMessage = createZapNotificationMessage(
            amount: amount,
            recipient: recipient,
            memo: memo,
            status: status,
            timestamp: zapDate
        )
        
        // Add to messages
        DispatchQueue.main.async {
            self.messages.append(zapMessage)
        }
    }
    
    /// Create a formatted chat message for zap notifications
    private func createZapNotificationMessage(
        amount: Int64,
        recipient: String,
        memo: String?,
        status: PaymentStatus,
        timestamp: Date
    ) -> KRTRMessage {
        
        let statusEmoji = status.emoji
        let amountText = formatSatsAmount(amount)
        
        var content = "\(statusEmoji) Zap: \(amountText) → \(recipient)"
        
        if let memo = memo, !memo.isEmpty {
            content += "\n💬 \(memo)"
        }
        
        return KRTRMessage(
            id: UUID().uuidString,
            content: content,
            senderID: "lightning_system",
            senderNickname: "⚡️ Lightning",
            timestamp: timestamp,
            channel: "#lightning",
            messageType: .system,
            mentions: [],
            isPrivate: false,
            recipientID: nil,
            recipientNickname: nil
        )
    }
    
    /// Format sats amount with appropriate units
    private func formatSatsAmount(_ amount: Int64) -> String {
        if amount >= 100_000_000 {
            let btc = Double(amount) / 100_000_000.0
            return String(format: "%.8f BTC", btc)
        } else if amount >= 1000 {
            let k = Double(amount) / 1000.0
            return String(format: "%.1fk sats", k)
        } else {
            return "\(amount) sats"
        }
    }
    
    /// Send a zap through the mesh network
    func sendZapThroughMesh(
        to recipient: String,
        amount: Int64,
        memo: String?,
        lightningService: LightningService
    ) {
        Task {
            do {
                let zap = try await lightningService.sendZap(
                    to: recipient,
                    amount: amount,
                    memo: memo
                )
                
                // The zap notification will be automatically broadcast
                // through the mesh network by the LightningService
                
                await MainActor.run {
                    // Update local message count
                    self.messagesSent += 1
                    self.updateNetworkStatus()
                }
                
            } catch {
                print("❌ Failed to send zap: \(error)")
                
                // Show error message in chat
                let errorMessage = KRTRMessage(
                    id: UUID().uuidString,
                    content: "❌ Failed to send zap: \(error.localizedDescription)",
                    senderID: "lightning_system",
                    senderNickname: "⚡️ Lightning",
                    timestamp: Date(),
                    channel: "#lightning",
                    messageType: .system,
                    mentions: [],
                    isPrivate: false,
                    recipientID: nil,
                    recipientNickname: nil
                )
                
                await MainActor.run {
                    self.messages.append(errorMessage)
                }
            }
        }
    }
    
    /// Check if a message contains Lightning-related content
    func isLightningMessage(_ content: String) -> Bool {
        let lightningKeywords = ["zap", "lightning", "sats", "bitcoin", "⚡", "₿"]
        let lowercaseContent = content.lowercased()
        
        return lightningKeywords.contains { keyword in
            lowercaseContent.contains(keyword)
        }
    }
    
    /// Extract Lightning invoice from message content
    func extractLightningInvoice(from content: String) -> String? {
        // Look for Lightning invoice patterns (lnbc, lnbcrt, lntb, etc.)
        let invoicePattern = #"(ln[a-z0-9]+)"#
        
        if let regex = try? NSRegularExpression(pattern: invoicePattern, options: .caseInsensitive) {
            let range = NSRange(content.startIndex..<content.endIndex, in: content)
            if let match = regex.firstMatch(in: content, options: [], range: range) {
                if let swiftRange = Range(match.range, in: content) {
                    return String(content[swiftRange])
                }
            }
        }
        
        return nil
    }
    
    /// Extract Lightning address from message content
    func extractLightningAddress(from content: String) -> String? {
        // Look for Lightning address patterns (<EMAIL>)
        let addressPattern = #"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"#
        
        if let regex = try? NSRegularExpression(pattern: addressPattern) {
            let range = NSRange(content.startIndex..<content.endIndex, in: content)
            if let match = regex.firstMatch(in: content, options: [], range: range) {
                if let swiftRange = Range(match.range, in: content) {
                    return String(content[swiftRange])
                }
            }
        }
        
        return nil
    }
}

// MARK: - PaymentStatus Extensions

extension PaymentStatus {
    var emoji: String {
        switch self {
        case .pending: return "⏳"
        case .succeeded: return "✅"
        case .failed: return "❌"
        case .cancelled: return "🚫"
        }
    }
}

// MARK: - KRTRMessage Extensions

extension KRTRMessage {
    /// Check if this message is a Lightning-related system message
    var isLightningSystemMessage: Bool {
        return senderNickname == "⚡️ Lightning" && messageType == .system
    }
    
    /// Extract zap amount from Lightning system message
    var zapAmount: Int64? {
        guard isLightningSystemMessage else { return nil }
        
        // Parse amount from content like "✅ Zap: 1000 sats → <EMAIL>"
        let pattern = #"(\d+(?:\.\d+)?k?) sats"#
        
        if let regex = try? NSRegularExpression(pattern: pattern) {
            let range = NSRange(content.startIndex..<content.endIndex, in: content)
            if let match = regex.firstMatch(in: content, options: [], range: range) {
                if let swiftRange = Range(match.range(at: 1), in: content) {
                    let amountString = String(content[swiftRange])
                    
                    if amountString.hasSuffix("k") {
                        let numberPart = String(amountString.dropLast())
                        if let number = Double(numberPart) {
                            return Int64(number * 1000)
                        }
                    } else {
                        return Int64(amountString)
                    }
                }
            }
        }
        
        return nil
    }
}

// MARK: - Lightning Message Handling

extension ChatViewModel {
    
    /// Process incoming mesh messages for Lightning content
    func processMessageForLightning(_ message: KRTRMessage) {
        // Check if message contains Lightning invoice or address
        if let invoice = extractLightningInvoice(from: message.content) {
            // Add quick zap action to message
            addLightningActionToMessage(message, invoice: invoice)
        } else if let address = extractLightningAddress(from: message.content) {
            // Add quick zap action to message
            addLightningActionToMessage(message, address: address)
        }
        
        // Check for Lightning system messages
        if message.content.contains("zap_notification") {
            handleLightningMessage(message.content)
        }
    }
    
    private func addLightningActionToMessage(_ message: KRTRMessage, invoice: String? = nil, address: String? = nil) {
        // This would be used to add interactive Lightning actions to messages
        // Implementation would depend on how the UI handles message actions
        print("💡 Lightning action available for message: \(message.id)")
    }
}
