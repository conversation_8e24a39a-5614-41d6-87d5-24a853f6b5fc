//
// QRCodeView.swift
// KRTR
//
// QR Code display for Lightning invoices and LNURL
//

import SwiftUI
import CoreImage.CIFilterBuiltins

struct QRCodeView: View {
    let data: String
    let title: String
    let subtitle: String?
    
    @State private var showingShareSheet = false
    @State private var qrImage: UIImage?
    
    init(data: String, title: String, subtitle: String? = nil) {
        self.data = data
        self.title = title
        self.subtitle = subtitle
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            VStack(spacing: 8) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            // QR Code
            VStack(spacing: 16) {
                if let qrImage = qrImage {
                    Image(uiImage: qrImage)
                        .interpolation(.none)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 250, height: 250)
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                } else {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 250, height: 250)
                        .overlay(
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                        )
                }
                
                // Data display (truncated)
                Text(truncatedData)
                    .font(.system(.caption, design: .monospaced))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
                    .padding(.horizontal)
            }
            
            // Action buttons
            HStack(spacing: 16) {
                Button(action: copyToClipboard) {
                    HStack {
                        Image(systemName: "doc.on.doc")
                        Text("Copy")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.blue.opacity(0.1))
                            .stroke(Color.blue, lineWidth: 1)
                    )
                    .foregroundColor(.blue)
                }
                
                Button(action: shareQRCode) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                        Text("Share")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.green.opacity(0.1))
                            .stroke(Color.green, lineWidth: 1)
                    )
                    .foregroundColor(.green)
                }
            }
            .padding(.horizontal)
        }
        .padding()
        .onAppear {
            generateQRCode()
        }
        .sheet(isPresented: $showingShareSheet) {
            if let qrImage = qrImage {
                ShareSheet(items: [qrImage, data])
            }
        }
    }
    
    private var truncatedData: String {
        if data.count > 100 {
            return String(data.prefix(50)) + "..." + String(data.suffix(50))
        }
        return data
    }
    
    private func generateQRCode() {
        let context = CIContext()
        let filter = CIFilter.qrCodeGenerator()
        
        filter.message = Data(data.utf8)
        filter.correctionLevel = "M"
        
        if let outputImage = filter.outputImage {
            // Scale up the QR code for better quality
            let transform = CGAffineTransform(scaleX: 10, y: 10)
            let scaledImage = outputImage.transformed(by: transform)
            
            if let cgImage = context.createCGImage(scaledImage, from: scaledImage.extent) {
                qrImage = UIImage(cgImage: cgImage)
            }
        }
    }
    
    private func copyToClipboard() {
        UIPasteboard.general.string = data
        
        // Show feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    private func shareQRCode() {
        showingShareSheet = true
    }
}

// MARK: - Lightning Invoice QR Code

struct LightningInvoiceQRView: View {
    let invoice: LightningInvoice
    
    var body: some View {
        QRCodeView(
            data: invoice.paymentRequest,
            title: "Lightning Invoice",
            subtitle: formatInvoiceSubtitle()
        )
    }
    
    private func formatInvoiceSubtitle() -> String {
        let amountText = "\(invoice.amount) sats"
        let memoText = invoice.memo.map { " • \($0)" } ?? ""
        return amountText + memoText
    }
}

// MARK: - LNURL QR Code

struct LNURLQRView: View {
    let lnurl: String
    let amount: Int64?
    let description: String?
    
    var body: some View {
        QRCodeView(
            data: lnurl,
            title: "Lightning Address",
            subtitle: formatLNURLSubtitle()
        )
    }
    
    private func formatLNURLSubtitle() -> String {
        var parts: [String] = []
        
        if let amount = amount {
            parts.append("\(amount) sats")
        }
        
        if let description = description {
            parts.append(description)
        }
        
        return parts.joined(separator: " • ")
    }
}

// MARK: - Compact QR Code

struct CompactQRCodeView: View {
    let data: String
    let size: CGFloat
    
    @State private var qrImage: UIImage?
    
    init(data: String, size: CGFloat = 100) {
        self.data = data
        self.size = size
    }
    
    var body: some View {
        Group {
            if let qrImage = qrImage {
                Image(uiImage: qrImage)
                    .interpolation(.none)
                    .resizable()
                    .scaledToFit()
                    .frame(width: size, height: size)
                    .background(Color.white)
                    .cornerRadius(8)
            } else {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: size, height: size)
                    .overlay(
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(0.6)
                    )
            }
        }
        .onAppear {
            generateQRCode()
        }
    }
    
    private func generateQRCode() {
        let context = CIContext()
        let filter = CIFilter.qrCodeGenerator()
        
        filter.message = Data(data.utf8)
        filter.correctionLevel = "M"
        
        if let outputImage = filter.outputImage {
            let transform = CGAffineTransform(scaleX: 10, y: 10)
            let scaledImage = outputImage.transformed(by: transform)
            
            if let cgImage = context.createCGImage(scaledImage, from: scaledImage.extent) {
                qrImage = UIImage(cgImage: cgImage)
            }
        }
    }
}

// MARK: - Share Sheet

struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: items, applicationActivities: nil)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Preview

struct QRCodeView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            QRCodeView(
                data: "lnbc1500n1ps0jyppqw508d6qejxtdg4y5r3zarvary0c5xw7kv8f3t5",
                title: "Lightning Invoice",
                subtitle: "1500 sats • Test payment"
            )
            
            CompactQRCodeView(
                data: "lnurl1dp68gurn8ghj7um9wfmxjcm99e3k7mf0v9cxj0m385ekvcenxc6r2c35xvukxefcv5mkvv34x5ekzd3ev56nyd3hxqurzepexejxxepnxscrvwfnv9nxzcn9xq6xyefhvgcxxcmyxymnserxfq5fns",
                size: 120
            )
        }
        .padding()
    }
}
