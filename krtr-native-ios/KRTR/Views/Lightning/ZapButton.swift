//
// ZapButton.swift
// KRTR
//
// Lightning Network zap button component
//

import SwiftUI

struct ZapButton: View {
    let recipient: String
    let onZap: (Int64, String?) -> Void
    
    @State private var showingZapSheet = false
    @State private var zapAmount: String = "1000"
    @State private var zapMemo: String = ""
    @State private var isZapping = false
    
    // Predefined zap amounts in sats
    private let quickAmounts: [Int64] = [100, 500, 1000, 5000, 10000]
    
    var body: some View {
        Button(action: {
            showingZapSheet = true
        }) {
            HStack(spacing: 4) {
                Image(systemName: "bolt.fill")
                    .foregroundColor(.yellow)
                Text("Zap")
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.blue.opacity(0.1))
                    .stroke(Color.blue, lineWidth: 1)
            )
        }
        .disabled(isZapping)
        .sheet(isPresented: $showingZapSheet) {
            ZapSheet(
                recipient: recipient,
                zapAmount: $zapAmount,
                zapMemo: $zapMemo,
                isZapping: $isZapping,
                quickAmounts: quickAmounts,
                onZap: onZap,
                onDismiss: {
                    showingZapSheet = false
                }
            )
        }
    }
}

struct ZapSheet: View {
    let recipient: String
    @Binding var zapAmount: String
    @Binding var zapMemo: String
    @Binding var isZapping: Bool
    let quickAmounts: [Int64]
    let onZap: (Int64, String?) -> Void
    let onDismiss: () -> Void
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "bolt.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.yellow)
                    
                    Text("Send Zap")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("to \(recipient)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top)
                
                // Quick amount buttons
                VStack(alignment: .leading, spacing: 12) {
                    Text("Quick Amounts")
                        .font(.headline)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                        ForEach(quickAmounts, id: \.self) { amount in
                            Button(action: {
                                zapAmount = String(amount)
                            }) {
                                VStack(spacing: 4) {
                                    Text("\(amount)")
                                        .font(.title3)
                                        .fontWeight(.semibold)
                                    Text("sats")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(zapAmount == String(amount) ? Color.blue.opacity(0.2) : Color.gray.opacity(0.1))
                                        .stroke(zapAmount == String(amount) ? Color.blue : Color.clear, lineWidth: 2)
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                
                // Custom amount input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Custom Amount")
                        .font(.headline)
                    
                    HStack {
                        TextField("Amount in sats", text: $zapAmount)
                            .keyboardType(.numberPad)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                        
                        Text("sats")
                            .foregroundColor(.secondary)
                    }
                }
                
                // Memo input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Memo (Optional)")
                        .font(.headline)
                    
                    TextField("Add a message...", text: $zapMemo, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3)
                }
                
                Spacer()
                
                // Send button
                Button(action: sendZap) {
                    HStack {
                        if isZapping {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "bolt.fill")
                        }
                        
                        Text(isZapping ? "Sending..." : "Send Zap")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(isValidAmount ? Color.blue : Color.gray)
                    )
                    .foregroundColor(.white)
                }
                .disabled(!isValidAmount || isZapping)
                .padding(.bottom)
            }
            .padding()
            .navigationTitle("Send Zap")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onDismiss()
                    }
                    .disabled(isZapping)
                }
            }
        }
    }
    
    private var isValidAmount: Bool {
        guard let amount = Int64(zapAmount), amount > 0 else { return false }
        return amount >= 1 && amount <= 1_000_000 // Max 1M sats
    }
    
    private func sendZap() {
        guard let amount = Int64(zapAmount), amount > 0 else { return }
        
        let memo = zapMemo.isEmpty ? nil : zapMemo
        onZap(amount, memo)
        onDismiss()
    }
}

// MARK: - Compact Zap Button

struct CompactZapButton: View {
    let recipient: String
    let amount: Int64
    let onZap: (Int64, String?) -> Void
    
    @State private var isZapping = false
    
    var body: some View {
        Button(action: {
            isZapping = true
            onZap(amount, nil)
            
            // Reset state after a delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                isZapping = false
            }
        }) {
            HStack(spacing: 4) {
                if isZapping {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .yellow))
                        .scaleEffect(0.6)
                } else {
                    Image(systemName: "bolt.fill")
                        .foregroundColor(.yellow)
                }
                
                Text("\(amount)")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                Capsule()
                    .fill(Color.black.opacity(0.1))
                    .stroke(Color.yellow, lineWidth: 1)
            )
        }
        .disabled(isZapping)
    }
}

// MARK: - Preview

struct ZapButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            ZapButton(recipient: "<EMAIL>") { amount, memo in
                print("Zapping \(amount) sats with memo: \(memo ?? "none")")
            }
            
            HStack {
                CompactZapButton(recipient: "<EMAIL>", amount: 100) { amount, memo in
                    print("Quick zap: \(amount) sats")
                }
                
                CompactZapButton(recipient: "<EMAIL>", amount: 500) { amount, memo in
                    print("Quick zap: \(amount) sats")
                }
                
                CompactZapButton(recipient: "<EMAIL>", amount: 1000) { amount, memo in
                    print("Quick zap: \(amount) sats")
                }
            }
        }
        .padding()
    }
}
