//
// LightningDashboardSection.swift
// KRTR
//
// Lightning Network dashboard section for integration with ZKDashboardView
//

import SwiftUI

struct LightningDashboardSection: View {
    @ObservedObject var lightningService: LightningService
    
    @State private var showingReceiveSheet = false
    @State private var showingSendSheet = false
    @State private var showingZapHistory = false
    @State private var receiveAmount: String = "1000"
    @State private var receiveMemo: String = ""
    
    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                HStack(spacing: 8) {
                    Image(systemName: "bolt.circle.fill")
                        .foregroundColor(.yellow)
                        .font(.title2)
                    
                    Text("Lightning")
                        .font(.title2)
                        .fontWeight(.bold)
                }
                
                Spacer()
                
                // Connection status
                HStack(spacing: 4) {
                    Circle()
                        .fill(lightningService.isConnected ? Color.green : Color.red)
                        .frame(width: 8, height: 8)
                    
                    Text(lightningService.isConnected ? "Connected" : "Disconnected")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            if lightningService.isConnected {
                connectedView
            } else {
                disconnectedView
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    @ViewBuilder
    private var connectedView: some View {
        VStack(spacing: 16) {
            // Balance and stats
            balanceSection
            
            // Quick actions
            quickActionsSection
            
            // Recent zaps
            if !lightningService.recentZaps.isEmpty {
                recentZapsSection
            }
        }
    }
    
    @ViewBuilder
    private var disconnectedView: some View {
        VStack(spacing: 12) {
            Image(systemName: "bolt.slash")
                .font(.system(size: 40))
                .foregroundColor(.gray)
            
            Text("Lightning Node Disconnected")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Text("Connect to a Lightning node to send and receive zaps")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Connect") {
                Task {
                    try? await lightningService.connect()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .padding(.vertical)
    }
    
    @ViewBuilder
    private var balanceSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Balance")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("\(lightningService.balance) sats")
                    .font(.title3)
                    .fontWeight(.semibold)
            }
            
            Spacer()
            
            if let nodeInfo = lightningService.nodeInfo {
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Channels")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(nodeInfo.activeChannelCount)/\(nodeInfo.channelCount)")
                        .font(.title3)
                        .fontWeight(.semibold)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.blue.opacity(0.1))
        )
    }
    
    @ViewBuilder
    private var quickActionsSection: some View {
        HStack(spacing: 12) {
            Button(action: {
                showingReceiveSheet = true
            }) {
                HStack {
                    Image(systemName: "qrcode")
                    Text("Receive")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.green.opacity(0.1))
                        .stroke(Color.green, lineWidth: 1)
                )
                .foregroundColor(.green)
            }
            
            Button(action: {
                showingSendSheet = true
            }) {
                HStack {
                    Image(systemName: "paperplane")
                    Text("Send")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.blue.opacity(0.1))
                        .stroke(Color.blue, lineWidth: 1)
                )
                .foregroundColor(.blue)
            }
        }
        .sheet(isPresented: $showingReceiveSheet) {
            ReceiveZapSheet(
                lightningService: lightningService,
                amount: $receiveAmount,
                memo: $receiveMemo
            )
        }
        .sheet(isPresented: $showingSendSheet) {
            SendZapSheet(lightningService: lightningService)
        }
    }
    
    @ViewBuilder
    private var recentZapsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Recent Zaps")
                    .font(.headline)
                
                Spacer()
                
                Button("View All") {
                    showingZapHistory = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            LazyVStack(spacing: 8) {
                ForEach(Array(lightningService.recentZaps.prefix(3))) { zap in
                    ZapRowView(zap: zap)
                }
            }
        }
        .sheet(isPresented: $showingZapHistory) {
            ZapHistoryView(lightningService: lightningService)
        }
    }
}

// MARK: - Zap Row View

struct ZapRowView: View {
    let zap: ZapTransaction
    
    var body: some View {
        HStack {
            // Status indicator
            Circle()
                .fill(zap.status.color)
                .frame(width: 8, height: 8)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(zap.recipient)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                if let memo = zap.memo, !memo.isEmpty {
                    Text(memo)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(zap.amount) sats")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(zap.timestamp, style: .relative)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Receive Zap Sheet

struct ReceiveZapSheet: View {
    @ObservedObject var lightningService: LightningService
    @Binding var amount: String
    @Binding var memo: String
    
    @State private var invoice: LightningInvoice?
    @State private var isGenerating = false
    @State private var error: String?
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if let invoice = invoice {
                    LightningInvoiceQRView(invoice: invoice)
                } else {
                    generateInvoiceForm
                }
            }
            .padding()
            .navigationTitle("Receive Zap")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                if invoice != nil {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("New") {
                            invoice = nil
                            error = nil
                        }
                    }
                }
            }
        }
    }
    
    @ViewBuilder
    private var generateInvoiceForm: some View {
        VStack(spacing: 20) {
            Text("Generate Lightning Invoice")
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Amount (sats)")
                    .font(.headline)
                
                TextField("Amount", text: $amount)
                    .keyboardType(.numberPad)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Memo (Optional)")
                    .font(.headline)
                
                TextField("Description", text: $memo, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3)
            }
            
            if let error = error {
                Text(error)
                    .foregroundColor(.red)
                    .font(.caption)
            }
            
            Spacer()
            
            Button(action: generateInvoice) {
                HStack {
                    if isGenerating {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "qrcode")
                    }
                    
                    Text(isGenerating ? "Generating..." : "Generate Invoice")
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isValidAmount ? Color.blue : Color.gray)
                )
                .foregroundColor(.white)
            }
            .disabled(!isValidAmount || isGenerating)
        }
    }
    
    private var isValidAmount: Bool {
        guard let amountValue = Int64(amount), amountValue > 0 else { return false }
        return amountValue >= 1 && amountValue <= 1_000_000
    }
    
    private func generateInvoice() {
        guard let amountValue = Int64(amount), amountValue > 0 else { return }
        
        isGenerating = true
        error = nil
        
        Task {
            do {
                let newInvoice = try await lightningService.createInvoice(
                    amount: amountValue,
                    memo: memo.isEmpty ? nil : memo
                )
                
                await MainActor.run {
                    self.invoice = newInvoice
                    self.isGenerating = false
                }
            } catch {
                await MainActor.run {
                    self.error = error.localizedDescription
                    self.isGenerating = false
                }
            }
        }
    }
}

// MARK: - Send Zap Sheet

struct SendZapSheet: View {
    @ObservedObject var lightningService: LightningService
    
    @State private var recipient: String = ""
    @State private var amount: String = "1000"
    @State private var memo: String = ""
    @State private var isSending = false
    @State private var error: String?
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Send Zap")
                    .font(.title2)
                    .fontWeight(.bold)
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Recipient")
                        .font(.headline)
                    
                    TextField("Lightning address or invoice", text: $recipient, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Amount (sats)")
                        .font(.headline)
                    
                    TextField("Amount", text: $amount)
                        .keyboardType(.numberPad)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Memo (Optional)")
                        .font(.headline)
                    
                    TextField("Message", text: $memo)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                if let error = error {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.caption)
                }
                
                Spacer()
                
                Button(action: sendZap) {
                    HStack {
                        if isSending {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "paperplane.fill")
                        }
                        
                        Text(isSending ? "Sending..." : "Send Zap")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(isValidInput ? Color.blue : Color.gray)
                    )
                    .foregroundColor(.white)
                }
                .disabled(!isValidInput || isSending)
            }
            .padding()
            .navigationTitle("Send Zap")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .disabled(isSending)
                }
            }
        }
    }
    
    private var isValidInput: Bool {
        guard !recipient.isEmpty,
              let amountValue = Int64(amount),
              amountValue > 0 else { return false }
        return amountValue >= 1 && amountValue <= lightningService.balance
    }
    
    private func sendZap() {
        guard let amountValue = Int64(amount), amountValue > 0 else { return }
        
        isSending = true
        error = nil
        
        Task {
            do {
                _ = try await lightningService.sendZap(
                    to: recipient,
                    amount: amountValue,
                    memo: memo.isEmpty ? nil : memo
                )
                
                await MainActor.run {
                    self.isSending = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    self.error = error.localizedDescription
                    self.isSending = false
                }
            }
        }
    }
}

// MARK: - Zap History View

struct ZapHistoryView: View {
    @ObservedObject var lightningService: LightningService
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(lightningService.recentZaps) { zap in
                    ZapRowView(zap: zap)
                        .listRowSeparator(.hidden)
                        .listRowBackground(Color.clear)
                }
            }
            .listStyle(PlainListStyle())
            .navigationTitle("Zap History")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Preview

struct LightningDashboardSection_Previews: PreviewProvider {
    static var previews: some View {
        let testService = LightningServiceFactory.createTestService()
        
        VStack {
            LightningDashboardSection(lightningService: testService)
            Spacer()
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
}
