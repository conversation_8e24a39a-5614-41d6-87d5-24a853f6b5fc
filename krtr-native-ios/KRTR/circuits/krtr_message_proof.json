{"noir_version": "1.0.0-beta.6+e796dfd67726cbc28eb9991782533b211025928d", "hash": "4882061762301994396", "abi": {"parameters": [{"name": "message_content", "type": {"kind": "field"}, "visibility": "private"}, {"name": "sender_private_key", "type": {"kind": "field"}, "visibility": "private"}, {"name": "nonce", "type": {"kind": "field"}, "visibility": "private"}, {"name": "message_hash", "type": {"kind": "field"}, "visibility": "public"}, {"name": "sender_public_key", "type": {"kind": "field"}, "visibility": "public"}, {"name": "timestamp", "type": {"kind": "integer", "sign": "unsigned", "width": 64}, "visibility": "public"}], "return_type": null, "error_types": {}}, "bytecode": "H4sIAAAAAAAA/7VUUW7DMAh1EifeWu1rF4HYru2/XWVR0/sfYa0GEqPZpCkECb0ErKcHGHfu2wL5w7q7D4Tj3T/cT/OEHIddFtGOC6694IpwSWkt84oRP2FuS82Q8nKpWDHXfJ1rjGtNtbSlFWiY4oq33OKNyAYzXXkZrbgwg5xFbzoLKJ1h/7zdHSle1SutUz3Yq3vHrFAHNuSaccteTISBG8ILHNzzAg8HNuyfXKC4cDLUFQ4apPWFm5zZAsNDm1dzZs29iA10bnTPxvW9EL4SnkTe7nGEmfnPx/BD2KjxJL7PKseL5d3vvdH/vcK/zuq4jL1t5JjznVDq5Tq+AI+F4PjJBwAA", "debug_symbols": "tZTRioMwEEX/Jc8+dJKYaH9lWSRqLIEQJdWFRfz3Ha1x24eAaPt0TSbnznCFGUmty+FWGNe0d3L9GknpjbXmVti2Ur1pHd6OU0LCsei91nhFnupIdcpr15OrG6xNyI+yw/Lo3im3aK88Vi8J0a5GRcPGWD1/Tck/fYmjHMQKc8Y3PN3Ns0xufBrjaZwHDsEAOKWHHGi6OTA4O8MbHMSBHLmAkGMW5eXpHOXpHOXpHOVHc8zz1SCl2QFeMLryQkT7Azv9I3ZbxHPcb3EkSCFDkBLyAzxAFpIEyF9XwzeeVGX8yzIkgC8RoSg4O0PJEsIfkj5EzDLNTb1RpdXrEm0GVz3t1P63C5WwdTvfVroevJ5bLjUc4g8=", "file_map": {"50": {"source": "// KRTR Message Authenticity Circuit\n// Proves message authenticity without revealing content\n\nfn main(\n    // Private inputs (not revealed)\n    message_content: Field,\n    sender_private_key: Field,\n    nonce: Field,\n\n    // Public inputs (revealed)\n    message_hash: pub Field,\n    sender_public_key: pub Field,\n    timestamp: pub u64\n) {\n    // 1. Verify message hash\n    let hash1 = hash_simple(message_content, timestamp as Field);\n    let computed_hash = hash_simple(hash1, nonce);\n    assert(computed_hash == message_hash);\n\n    // 2. Verify sender's public key matches private key (simplified)\n    let computed_public_key = hash_simple(sender_private_key, 1);\n    assert(computed_public_key == sender_public_key);\n    // Note: This is simplified - in practice would use proper Ed25519 key derivation\n\n    // 3. Create signature proof (simplified)\n    let hash_input = hash_simple(message_hash, sender_private_key);\n    let signature_hash = hash_simple(hash_input, timestamp as Field);\n\n    // 4. Verify timestamp is reasonable (within last 24 hours)\n    // This prevents replay attacks with old proofs\n    // Note: In practice, would compare against current time\n    assert(timestamp > 0);\n}\n\n// Simple hash function for demonstration\nfn hash_simple(a: Field, b: Field) -> Field {\n    // Use a simple but secure hash based on field arithmetic\n    // The modulus is automatically handled by the Field type\n    a * 7 + b * 13 + 42\n}\n", "path": "/Users/<USER>/Desktop/AAA Shibumi Crypto/AAA_KRTR_MESH/krtr-mesh/circuits/message_proof/src/main.nr"}}, "names": ["main"], "brillig_names": ["directive_integer_quotient"]}