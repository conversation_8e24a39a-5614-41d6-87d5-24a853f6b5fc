name: Check

on:
  pull_request:
    branches:
      - main

jobs:
  check:
    runs-on: macos-15
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Set up Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: "16.4"
    - name: XcodeGen
      uses: x<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/xcodegen-action@1.2.4
      with:
        spec: project.yml
        version: 2.43.0
    - name: Run iOS tests
      run: |
        set -o pipefail && xcodebuild test \
          -scheme KRTRTests_iOS \
          -destination "platform=iOS Simulator,name=iPhone 16" | xcpretty
