//
// LightningIntegrationTests.swift
// KRTRTests
//
// Tests for Lightning Network integration with KRTR mesh
//

import XCTest
@testable import KRTR

class LightningIntegrationTests: XCTestCase {
    
    var lightningService: LightningService!
    var meshService: BluetoothMeshService!
    var chatViewModel: ChatViewModel!
    
    override func setUpWithError() throws {
        // Create test services
        meshService = BluetoothMeshService()
        lightningService = LightningServiceFactory.createTestService()
        chatViewModel = ChatViewModel()
    }
    
    override func tearDownWithError() throws {
        lightningService = nil
        meshService = nil
        chatViewModel = nil
    }
    
    // MARK: - Lightning Service Tests
    
    func testLightningServiceInitialization() throws {
        XCTAssertNotNil(lightningService)
        XCTAssertFalse(lightningService.isConnected)
        XCTAssertEqual(lightningService.balance, 0)
        XCTAssertTrue(lightningService.recentZaps.isEmpty)
    }
    
    func testZapTransactionCreation() throws {
        let zap = ZapTransaction(
            amount: 1000,
            recipient: "<EMAIL>",
            memo: "Test zap"
        )
        
        XCTAssertEqual(zap.amount, 1000)
        XCTAssertEqual(zap.recipient, "<EMAIL>")
        XCTAssertEqual(zap.memo, "Test zap")
        XCTAssertEqual(zap.status, .pending)
        XCTAssertNotNil(zap.id)
    }
    
    func testPaymentStatusColors() throws {
        XCTAssertEqual(PaymentStatus.pending.color, .orange)
        XCTAssertEqual(PaymentStatus.succeeded.color, .green)
        XCTAssertEqual(PaymentStatus.failed.color, .red)
        XCTAssertEqual(PaymentStatus.cancelled.color, .gray)
    }
    
    func testLightningAddressValidation() throws {
        let validAddress = LightningAddress(
            address: "<EMAIL>",
            lnurl: "lnurl1dp68gurn8ghj7um9wfmxjcm99e3k7mf0v9cxj0m385ekvcenxc6r2c35xvukxefcv5mkvv34x5ekzd3ev56nyd3hxqurzepexejxxepnxscrvwfnv9nxzcn9xq6xyefhvgcxxcmyxymnserxfq5fns",
            minSendable: 1000,
            maxSendable: 100000,
            metadata: "Test metadata",
            callback: URL(string: "https://example.com/callback")!
        )
        
        XCTAssertTrue(validAddress.isValid)
        
        let invalidAddress = LightningAddress(
            address: "invalid-address",
            lnurl: "",
            minSendable: 1000,
            maxSendable: 100000,
            metadata: "Test metadata",
            callback: URL(string: "https://example.com/callback")!
        )
        
        XCTAssertFalse(invalidAddress.isValid)
    }
    
    // MARK: - Chat Integration Tests
    
    func testLightningMessageDetection() throws {
        XCTAssertTrue(chatViewModel.isLightningMessage("Send me some sats"))
        XCTAssertTrue(chatViewModel.isLightningMessage("⚡ Lightning payment"))
        XCTAssertTrue(chatViewModel.isLightningMessage("Bitcoin zap incoming"))
        XCTAssertFalse(chatViewModel.isLightningMessage("Regular message"))
    }
    
    func testLightningInvoiceExtraction() throws {
        let messageWithInvoice = "Please pay this invoice: lnbc1500n1ps0jyppqw508d6qejxtdg4y5r3zarvary0c5xw7kv8f3t5"
        let extractedInvoice = chatViewModel.extractLightningInvoice(from: messageWithInvoice)
        
        XCTAssertNotNil(extractedInvoice)
        XCTAssertTrue(extractedInvoice!.hasPrefix("lnbc"))
        
        let messageWithoutInvoice = "Just a regular message"
        let noInvoice = chatViewModel.extractLightningInvoice(from: messageWithoutInvoice)
        
        XCTAssertNil(noInvoice)
    }
    
    func testLightningAddressExtraction() throws {
        let messageWithAddress = "Send <NAME_EMAIL>"
        let extractedAddress = chatViewModel.extractLightningAddress(from: messageWithAddress)
        
        XCTAssertEqual(extractedAddress, "<EMAIL>")
        
        let messageWithoutAddress = "No address here"
        let noAddress = chatViewModel.extractLightningAddress(from: messageWithoutAddress)
        
        XCTAssertNil(noAddress)
    }
    
    func testZapNotificationMessageCreation() throws {
        let zapNotificationJSON = """
        {
            "type": "zap_notification",
            "amount": 1000,
            "recipient": "<EMAIL>",
            "memo": "Test payment",
            "status": "succeeded",
            "timestamp": \(Date().timeIntervalSince1970)
        }
        """
        
        let initialMessageCount = chatViewModel.messages.count
        chatViewModel.handleLightningMessage(zapNotificationJSON)
        
        // Wait for async processing
        let expectation = XCTestExpectation(description: "Message processed")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        XCTAssertEqual(chatViewModel.messages.count, initialMessageCount + 1)
        
        let lastMessage = chatViewModel.messages.last!
        XCTAssertEqual(lastMessage.senderNickname, "⚡️ Lightning")
        XCTAssertTrue(lastMessage.content.contains("1000 sats"))
        XCTAssertTrue(lastMessage.content.contains("<EMAIL>"))
    }
    
    // MARK: - Amount Formatting Tests
    
    func testSatsAmountFormatting() throws {
        // Test small amounts
        XCTAssertEqual(chatViewModel.formatSatsAmount(100), "100 sats")
        XCTAssertEqual(chatViewModel.formatSatsAmount(999), "999 sats")
        
        // Test thousands
        XCTAssertEqual(chatViewModel.formatSatsAmount(1000), "1.0k sats")
        XCTAssertEqual(chatViewModel.formatSatsAmount(5500), "5.5k sats")
        
        // Test Bitcoin amounts
        XCTAssertEqual(chatViewModel.formatSatsAmount(100_000_000), "1.00000000 BTC")
        XCTAssertEqual(chatViewModel.formatSatsAmount(150_000_000), "1.50000000 BTC")
    }
    
    // MARK: - Error Handling Tests
    
    func testLightningErrorTypes() throws {
        let nodeError = LightningError.nodeNotConnected
        XCTAssertEqual(nodeError.localizedDescription, "Lightning node is not connected")
        
        let invoiceError = LightningError.invalidInvoice
        XCTAssertEqual(invoiceError.localizedDescription, "Invalid Lightning invoice")
        
        let balanceError = LightningError.insufficientBalance
        XCTAssertEqual(balanceError.localizedDescription, "Insufficient balance for payment")
        
        let paymentError = LightningError.paymentFailed("Network timeout")
        XCTAssertEqual(paymentError.localizedDescription, "Payment failed: Network timeout")
    }
    
    // MARK: - ZK Integration Tests
    
    func testZKProofZapCreation() throws {
        let zkProofHash = "test_proof_hash_123"
        
        let zkZap = ZapTransaction(
            amount: 2000,
            recipient: "<EMAIL>",
            memo: "ZK proof payment",
            zkProofHash: zkProofHash
        )
        
        XCTAssertEqual(zkZap.zkProofHash, zkProofHash)
        XCTAssertEqual(zkZap.amount, 2000)
        XCTAssertEqual(zkZap.recipient, "<EMAIL>")
    }
    
    // MARK: - Performance Tests
    
    func testZapTransactionPerformance() throws {
        measure {
            for _ in 0..<1000 {
                let _ = ZapTransaction(
                    amount: Int64.random(in: 100...10000),
                    recipient: "<EMAIL>",
                    memo: "Performance test"
                )
            }
        }
    }
    
    func testLightningMessageProcessingPerformance() throws {
        let testMessages = (0..<100).map { index in
            "Test message \(index) with lightning content and sats"
        }
        
        measure {
            for message in testMessages {
                _ = chatViewModel.isLightningMessage(message)
                _ = chatViewModel.extractLightningInvoice(from: message)
                _ = chatViewModel.extractLightningAddress(from: message)
            }
        }
    }
}

// MARK: - Test Extensions

extension ChatViewModel {
    // Expose private methods for testing
    func formatSatsAmount(_ amount: Int64) -> String {
        if amount >= 100_000_000 {
            let btc = Double(amount) / 100_000_000.0
            return String(format: "%.8f BTC", btc)
        } else if amount >= 1000 {
            let k = Double(amount) / 1000.0
            return String(format: "%.1fk sats", k)
        } else {
            return "\(amount) sats"
        }
    }
}
