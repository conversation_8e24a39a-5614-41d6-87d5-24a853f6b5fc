# Xcode
#
# gitignore contributors: remember to update Global/Xcode.gitignore, Objective-C.gitignore & Swift.gitignore

## User settings
xcuserdata/

## compatibility with Xcode 8 and earlier (ignoring not required starting Xcode 9)
*.xcscmblueprint
*.xccheckout

## compatibility with Xcode 3 and earlier (ignoring not required starting Xcode 4)
build/
DerivedData/
*.moved-aside
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3

## Gcc Patch
/*.gcno

## macOS
.DS_Store

## SPM
.swiftpm
.build/

## CocoaPods
Pods/

## Carthage
Carthage/Build/

## fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

## Code Injection
iOSInjectionProject/

## Xcode project
*.xcodeproj/project.xcworkspace/
*.xcodeproj/xcshareddata/

## Python
__pycache__/
*.py[cod]
*$py.class

## Temporary files
*.tmp
*.temp