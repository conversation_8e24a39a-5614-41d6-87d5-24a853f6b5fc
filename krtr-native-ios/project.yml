name: KRTR
options:
  bundleIdPrefix: com.krtr.mesh
  deploymentTarget:
    iOS: 16.0
    macOS: 13.0
  createIntermediateGroups: true

settings:
  MARKETING_VERSION: 1.0.0
  CURRENT_PROJECT_VERSION: 1

targets:
  KRTR_iOS:
    type: application
    platform: iOS
    sources:
      - KRTR
    resources:
      - KRTR/Assets.xcassets
      - KRTR/LaunchScreen.storyboard
    info:
      path: KRTR/Info.plist
      properties:
        CFBundleDisplayName: KRTR
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleVersion: $(CURRENT_PROJECT_VERSION)
        NSBluetoothAlwaysUsageDescription: KRTR uses Bluetooth to create a secure mesh network for decentralized messaging with nearby users.
        NSBluetoothPeripheralUsageDescription: KRTR uses Bluetooth to discover and connect with other KRTR users nearby for offline messaging.
        UIBackgroundModes:
          - bluetooth-central
          - bluetooth-peripheral
        UILaunchStoryboardName: LaunchScreen
        UISupportedInterfaceOrientations:
          - UIInterfaceOrientationPortrait
        UISupportedInterfaceOrientations~ipad:
          - UIInterfaceOrientationPortrait
          - UIInterfaceOrientationPortraitUpsideDown
          - UIInterfaceOrientationLandscapeLeft
          - UIInterfaceOrientationLandscapeRight
        UIRequiresFullScreen: false
        CFBundleURLTypes:
          - CFBundleURLSchemes:
              - krtr
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.krtr.mesh
      PRODUCT_NAME: KRTR
      INFOPLIST_FILE: KRTR/Info.plist
      ENABLE_PREVIEWS: YES
      SWIFT_VERSION: 5.0
      IPHONEOS_DEPLOYMENT_TARGET: 16.0
      SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD: YES
      CODE_SIGN_STYLE: Automatic
      CODE_SIGNING_REQUIRED: YES
      CODE_SIGNING_ALLOWED: YES
      DEVELOPMENT_TEAM: "445WRT8WT6"
      ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
      ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS: YES
      CODE_SIGN_ENTITLEMENTS: KRTR/KRTR.entitlements
    dependencies:
      - target: KRTRShareExtension
        embed: true

  KRTR_macOS:
    type: application
    platform: macOS
    sources:
      - KRTR
    resources:
      - KRTR/Assets.xcassets
    info:
      path: KRTR/Info.plist
      properties:
        CFBundleDisplayName: KRTR
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleVersion: $(CURRENT_PROJECT_VERSION)
        LSMinimumSystemVersion: $(MACOSX_DEPLOYMENT_TARGET)
        NSBluetoothAlwaysUsageDescription: KRTR uses Bluetooth to create a secure mesh network for decentralized messaging with nearby users.
        NSBluetoothPeripheralUsageDescription: KRTR uses Bluetooth to discover and connect with other KRTR users nearby for offline messaging.
        CFBundleURLTypes:
          - CFBundleURLSchemes:
              - krtr
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.krtr.mesh
      PRODUCT_NAME: KRTR
      INFOPLIST_FILE: KRTR/Info.plist
      ENABLE_PREVIEWS: YES
      SWIFT_VERSION: 5.0
      MACOSX_DEPLOYMENT_TARGET: 13.0
      CODE_SIGN_STYLE: Automatic
      CODE_SIGNING_REQUIRED: YES
      CODE_SIGNING_ALLOWED: YES
      DEVELOPMENT_TEAM: "445WRT8WT6"
      ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
      ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS: YES
      CODE_SIGN_ENTITLEMENTS: KRTR/KRTR-macOS.entitlements

  KRTRShareExtension:
    type: app-extension
    platform: iOS
    sources:
      - KRTRShareExtension
    info:
      path: KRTRShareExtension/Info.plist
      properties:
        CFBundleDisplayName: KRTR
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleVersion: $(CURRENT_PROJECT_VERSION)
        NSExtension:
          NSExtensionPointIdentifier: com.apple.share-services
          NSExtensionPrincipalClass: $(PRODUCT_MODULE_NAME).ShareViewController
          NSExtensionAttributes:
            NSExtensionActivationRule:
              NSExtensionActivationSupportsText: true
              NSExtensionActivationSupportsWebURLWithMaxCount: 1
              NSExtensionActivationSupportsImageWithMaxCount: 1
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.krtr.mesh.ShareExtension
      INFOPLIST_FILE: KRTRShareExtension/Info.plist
      SWIFT_VERSION: 5.0
      IPHONEOS_DEPLOYMENT_TARGET: 16.0
      CODE_SIGN_STYLE: Automatic
      CODE_SIGNING_REQUIRED: YES
      CODE_SIGNING_ALLOWED: YES
      DEVELOPMENT_TEAM: "445WRT8WT6"
      CODE_SIGN_ENTITLEMENTS: KRTRShareExtension/KRTRShareExtension.entitlements
      CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION: YES

  KRTRTests_iOS:
    type: bundle.unit-test
    platform: iOS
    sources:
      - KRTRTests
    dependencies:
      - target: KRTR_iOS
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.krtr.mesh.tests
      INFOPLIST_FILE: KRTRTests/Info.plist
      SWIFT_VERSION: 5.0
      IPHONEOS_DEPLOYMENT_TARGET: 16.0
      TEST_HOST: $(BUILT_PRODUCTS_DIR)/KRTR.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/KRTR
      BUNDLE_LOADER: $(TEST_HOST)
      CODE_SIGN_STYLE: Automatic
      CODE_SIGNING_REQUIRED: YES
      CODE_SIGNING_ALLOWED: YES
      DEVELOPMENT_TEAM: "445WRT8WT6"

  KRTRTests_macOS:
    type: bundle.unit-test
    platform: macOS
    sources:
      - KRTRTests
    dependencies:
      - target: KRTR_macOS
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.krtr.mesh.tests
      INFOPLIST_FILE: KRTRTests/Info.plist
      SWIFT_VERSION: 5.0
      MACOSX_DEPLOYMENT_TARGET: 13.0
      TEST_HOST: $(BUILT_PRODUCTS_DIR)/KRTR.app/Contents/MacOS/KRTR
      BUNDLE_LOADER: $(TEST_HOST)
      CODE_SIGN_STYLE: Automatic
      CODE_SIGNING_REQUIRED: YES
      CODE_SIGNING_ALLOWED: YES
      DEVELOPMENT_TEAM: "445WRT8WT6"

schemes:
  KRTR (iOS):
    build:
      targets:
        KRTR_iOS: all
        KRTRShareExtension: all
    run:
      config: Debug
      executable: KRTR_iOS
    test:
      config: Debug
      targets:
        - KRTRTests_iOS
    profile:
      config: Release
      executable: KRTR_iOS
    analyze:
      config: Debug
    archive:
      config: Release

  KRTR (macOS):
    build:
      targets:
        KRTR_macOS: all
    run:
      config: Debug
      executable: KRTR_macOS
    test:
      config: Debug
      targets:
        - KRTRTests_macOS
    profile:
      config: Release
      executable: KRTR_macOS
    analyze:
      config: Debug
    archive:
      config: Release
