#!/bin/bash

# KRTR Mesh iOS Installation Script
# This script builds and installs the KRTR Mesh app on a connected iOS device

set -e

# Load environment variables
if [ -f .env ]; then
    source .env
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 KRTR Mesh iOS Installation Script${NC}"
echo "=================================="

# Check if we have the required environment variables
if [ -z "$APPLE_DEVELOPMENT_TEAM" ]; then
    echo -e "${RED}❌ Error: APPLE_DEVELOPMENT_TEAM not set in .env file${NC}"
    exit 1
fi

if [ -z "$APPLE_DEVELOPER_EMAIL" ]; then
    echo -e "${RED}❌ Error: APPLE_DEVELOPER_EMAIL not set in .env file${NC}"
    exit 1
fi

echo -e "${YELLOW}📱 Checking for connected iOS devices...${NC}"

# List connected devices
xcrun xctrace list devices | grep -E "iPad|iPhone" | grep -v "Simulator" | grep -v "Offline"

echo -e "${YELLOW}🔍 Looking for iOS devices...${NC}"

# Find connected iPhone or iPad
DEVICE_ID=$(xcrun xctrace list devices | grep -E "iPhone|iPad" | grep -v "Simulator" | grep -v "Offline" | head -1 | grep -o '([^)]*-[^)]*)' | tr -d '()')
DEVICE_NAME=$(xcrun xctrace list devices | grep -E "iPhone|iPad" | grep -v "Simulator" | grep -v "Offline" | head -1 | sed 's/(.*//')

if [ -z "$DEVICE_ID" ]; then
    echo -e "${RED}❌ No iPhone or iPad found. Please connect your device and trust this computer.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found device: $DEVICE_NAME with ID: $DEVICE_ID${NC}"

echo -e "${YELLOW}🔨 Building KRTR Mesh for iOS...${NC}"

# Build the app
xcodebuild \
    -scheme "KRTRMesh (iOS)" \
    -destination "platform=iOS,id=$DEVICE_ID" \
    -configuration Debug \
    DEVELOPMENT_TEAM="$APPLE_DEVELOPMENT_TEAM" \
    CODE_SIGN_IDENTITY="Apple Development" \
    build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build successful!${NC}"
else
    echo -e "${RED}❌ Build failed. Check the output above for errors.${NC}"
    exit 1
fi

echo -e "${YELLOW}📲 Installing app on device...${NC}"

# Install the app
xcodebuild \
    -scheme "KRTRMesh (iOS)" \
    -destination "platform=iOS,id=$DEVICE_ID" \
    -configuration Debug \
    DEVELOPMENT_TEAM="$APPLE_DEVELOPMENT_TEAM" \
    CODE_SIGN_IDENTITY="Apple Development" \
    install

if [ $? -eq 0 ]; then
    echo -e "${GREEN}🎉 KRTR Mesh successfully installed on your device!${NC}"
    echo -e "${GREEN}You can now find the app on your device's home screen.${NC}"
    echo -e "${YELLOW}📱 Navigation improvements added:${NC}"
    echo -e "${YELLOW}   • Tab bar now always visible at bottom${NC}"
    echo -e "${YELLOW}   • Clear 'Chat' indicator in header${NC}"
    echo -e "${YELLOW}   • Swipe up hint for tab navigation${NC}"
else
    echo -e "${RED}❌ Installation failed. The app was built but couldn't be installed.${NC}"
    echo -e "${YELLOW}💡 Try running the app directly from Xcode or check device trust settings.${NC}"
fi

echo -e "${GREEN}✨ Installation script completed!${NC}"
