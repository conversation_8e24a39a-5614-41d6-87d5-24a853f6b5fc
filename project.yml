name: KRTRMesh
options:
  bundleIdPrefix: com.krtr.mesh
  deploymentTarget:
    iOS: 16.0
    macOS: 13.0
  createIntermediateGroups: true
settings:
  MARKETING_VERSION: 1.0.0
  CURRENT_PROJECT_VERSION: 1

targets:
  KRTRMesh_iOS:
    type: application
    platform: iOS
    sources:
      - KRTRMesh
    resources:
      - KRTRMesh/Assets.xcassets
      - KRTRMesh/LaunchScreen.storyboard
    info:
      path: KRTRMesh/Info.plist
      properties:
        CFBundleDisplayName: KRTR Mesh
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleVersion: $(CURRENT_PROJECT_VERSION)
        NSBluetoothAlwaysUsageDescription: KRT<PERSON> <PERSON><PERSON> uses Bluetooth to create a secure mesh network for decentralized messaging with nearby users.
        NSBluetoothPeripheralUsageDescription: KRTR Mesh uses Bluetooth to discover and connect with other KRTR users nearby for offline messaging.
        UIBackgroundModes:
          - bluetooth-central
          - bluetooth-peripheral
        UILaunchStoryboardName: LaunchScreen
        UISupportedInterfaceOrientations:
          - UIInterfaceOrientationPortrait
        UISupportedInterfaceOrientations~ipad:
          - UIInterfaceOrientationPortrait
          - UIInterfaceOrientationPortraitUpsideDown
          - UIInterfaceOrientationLandscapeLeft
          - UIInterfaceOrientationLandscapeRight
        UIRequiresFullScreen: false
        CFBundleURLTypes:
          - CFBundleURLSchemes:
              - krtr
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.krtr.mesh
      PRODUCT_NAME: KRTRMesh
      INFOPLIST_FILE: KRTRMesh/Info.plist
      ENABLE_PREVIEWS: YES
      SWIFT_VERSION: 5.0
      IPHONEOS_DEPLOYMENT_TARGET: 16.0
      SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD: YES
      CODE_SIGN_STYLE: Automatic
      CODE_SIGNING_REQUIRED: YES
      CODE_SIGNING_ALLOWED: YES
      DEVELOPMENT_TEAM: "445WRT8WT6"
      ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
      ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS: YES
      CODE_SIGN_ENTITLEMENTS: KRTRMesh/KRTRMesh.entitlements

  KRTRMesh_macOS:
    type: application
    platform: macOS
    sources:
      - KRTRMesh
    resources:
      - KRTRMesh/Assets.xcassets
    info:
      path: KRTRMesh/Info.plist
      properties:
        CFBundleDisplayName: KRTR Mesh
        CFBundleShortVersionString: $(MARKETING_VERSION)
        CFBundleVersion: $(CURRENT_PROJECT_VERSION)
        LSMinimumSystemVersion: $(MACOSX_DEPLOYMENT_TARGET)
        NSBluetoothAlwaysUsageDescription: KRTR Mesh uses Bluetooth to create a secure mesh network for decentralized messaging with nearby users.
        NSBluetoothPeripheralUsageDescription: KRTR Mesh uses Bluetooth to discover and connect with other KRTR users nearby for offline messaging.
        CFBundleURLTypes:
          - CFBundleURLSchemes:
              - krtr
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.krtr.mesh
      PRODUCT_NAME: KRTRMesh
      INFOPLIST_FILE: KRTRMesh/Info.plist
      ENABLE_PREVIEWS: YES
      SWIFT_VERSION: 5.0
      MACOSX_DEPLOYMENT_TARGET: 13.0
      CODE_SIGN_STYLE: Automatic
      CODE_SIGNING_REQUIRED: YES
      CODE_SIGNING_ALLOWED: YES
      DEVELOPMENT_TEAM: "445WRT8WT6"
      ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
      ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS: YES
      CODE_SIGN_ENTITLEMENTS: KRTRMesh/KRTRMesh-macOS.entitlements

schemes:
  KRTRMesh (iOS):
    build:
      targets:
        KRTRMesh_iOS: all
    run:
      config: Debug
      executable: KRTRMesh_iOS
    test:
      config: Debug
    profile:
      config: Release
      executable: KRTRMesh_iOS
    analyze:
      config: Debug
    archive:
      config: Release

  KRTRMesh (macOS):
    build:
      targets:
        KRTRMesh_macOS: all
    run:
      config: Debug
      executable: KRTRMesh_macOS
    test:
      config: Debug
    profile:
      config: Release
      executable: KRTRMesh_macOS
    analyze:
      config: Debug
    archive:
      config: Release
